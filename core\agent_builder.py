# core/agent_builder.py

import logging
import json
import re
from typing import Dict, Any, Optional, List

from ..utils.response_formatter import format_response
from ..utils.toolkit import ConfigUtils
from ..core.agent_registry import ToolRegistry
from ..core.tools import Tool
from ..utils.context_manager import ContextManager

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


def _get_llm_settings(config: Dict[str, Any]) -> tuple:
    llm_config = config.get("llm", {})
    return llm_config.get("model"), llm_config.get("temperature")


def _build_tool_selection_prompt(user_input: str, tools: list, context_messages: list = None, config: dict = None) -> str:
    config = config or {}

    if not tools:
        return config.get("no_tools_prompt", f"""
Analyze this user request and provide a helpful direct response since no tools are available:

User request: {user_input}

Respond with JSON:
{{"selected_tool": "none", "parameters": {{}}, "reasoning": "No tools available", "confidence": 1.0, "direct_response": "your_helpful_response"}}
""")

    tool_descriptions = []
    for tool in tools:
        desc = config.get("tool_description_template", "• {name}: {description}").format(
            name=tool.name,
            description=tool.description
        )
        if hasattr(tool, 'schema') and tool.schema and tool.schema.get('properties'):
            params = list(tool.schema['properties'].keys())
            if params:
                desc += config.get("tool_params_template", " (Parameters: {params})").format(
                    params=", ".join(params)
                )
        tool_descriptions.append(desc)

    tools_text = "\n".join(tool_descriptions)
    context_text = config.get("context_available_text", "\n\nRecent context available.") if context_messages else ""

    return config.get("tool_selection_prompt", f"""
Analyze the user's request and determine the best approach.

Available tools:
{tools_text}

User request: {user_input}{context_text}

Respond with JSON only:
{{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "brief_explanation", "confidence": 0.95, "direct_response": "response_if_no_tool_needed"}}
""")


def _enhance_tool_parameters(params: Dict[str, Any], tool: Tool, config: dict, model: str, temperature: float) -> Dict[str, Any]:
    if not config.get("enable_parameter_enhancement", False):
        return params

    try:
        prompt = config.get("parameter_enhancement_prompt", f"""
You are helping to optimize tool parameters.

Tool: {tool.name}
Description: {tool.description}
Current parameters: {json.dumps(params, indent=2)}
Original user query: {params.get('original_query', 'N/A')}

Suggest any improvements or additions. Return as JSON.
""")
        result = format_response(
            prompt=prompt,
            formatter="json",
            model_name=model,
            temperature=temperature,
            return_meta=True
        )
        if result and not result.get("error"):
            enhanced = result.get("parsed_response", params)
            if isinstance(enhanced, dict):
                logger.info(f"🔧 Enhanced tool parameters: {enhanced}")
                return enhanced
    except Exception as e:
        logger.warning(f"Parameter enhancement failed: {e}")
    return params


def _ai_error_recovery(error: str, tool_name: str, user_input: str, registry: ToolRegistry, config: dict, model: str, temperature: float) -> str:
    if not config.get("enable_ai_error_recovery", False):
        return config.get("error_response_template", f"Error occurred with {tool_name}: {error}").format(
            tool_name=tool_name, error=error)

    try:
        alternatives = [t.name for t in registry.get_tools() if t.name != tool_name]
        recovery_prompt = config.get("error_recovery_prompt", """
The tool '{tool_name}' failed.
Error: {error}

Original request: {user_input}
Alternatives: {alternatives}

Suggest a recovery or alternative solution.
""").format(
            user_input=user_input,
            tool_name=tool_name,
            error=error,
            alternatives=", ".join(alternatives[:3])
        )

        return format_response(
            prompt=recovery_prompt,
            formatter="answer",
            model_name=model,
            temperature=temperature
        ) or config.get("error_response_template", f"Tool {tool_name} encountered an error: {error}")
    except Exception as e:
        logger.warning(f"AI error recovery failed: {e}")
        return config.get("error_response_template", f"Tool {tool_name} encountered an error: {error}")


def _get_minimal_config() -> Dict[str, Any]:
    return {
        "llm": {
            "model": "gpt-4o-mini",
            "temperature": 0.7
        },
        "context_limit": 5,
        "context_format": "compact",
        "enable_parameter_enhancement": False,
        "enable_ai_error_recovery": True
    }


def _extract_tool_decision(llm_response: str, config: dict = None) -> Dict[str, Any]:
    logger.info(f"Processing LLM response: {llm_response[:200]}...")
    if not llm_response or not llm_response.strip():
        return {
            "selected_tool": "none",
            "parameters": {},
            "reasoning": "Empty response from LLM",
            "direct_response": config.get("empty_response_message", "I didn't receive a proper response. Please try again.")
        }

    cleaned_response = llm_response.strip()
    patterns = (config or {}).get("json_extraction", {}).get("patterns", [
        r'\{.*?\}',
        r'```json\s*(\{.*?\})\s*```',
        r'\{[^{}]*"selected_tool"[^{}]*\}',
    ])

    for pattern in patterns:
        try:
            match = re.search(pattern, cleaned_response, re.DOTALL)
            if match:
                json_str = match.group(1) if 'group(1)' in pattern else match.group()
                result = json.loads(json_str)
                if isinstance(result, dict) and "selected_tool" in result:
                    return result
        except Exception as e:
            logger.debug(f"Pattern {pattern} failed: {e}")

    return {
        "selected_tool": "none",
        "parameters": {},
        "reasoning": "Could not parse LLM response",
        "direct_response": cleaned_response
    }


def agent_executor(user_input: str, config=None, user_id: str = "default_user",
                   tools: Optional[List] = None, llm_client=None, context_manager=None):
    logger.info("🚀 Starting universal AI orchestrator...")

    config = {**_get_minimal_config(), **(config or {})}
    if "llm" in config and "llm" in _get_minimal_config():
        config["llm"] = {**_get_minimal_config()["llm"], **config["llm"]}

    model, temperature = _get_llm_settings(config)

    system_prompt = config.get("system_prompt", """
You are an AI orchestrator that routes user queries to appropriate tools. 
Analyze requests, select the best tool if available, or provide helpful responses directly.
""")

    context_manager = context_manager or ContextManager()
    registry = ToolRegistry()

    if tools:
        from .tool_registration import register_dynamic_tools
        register_dynamic_tools(registry, tools, llm_client)

    if config.get("discovery", {}).get("enabled", False):
        from .tool_discovery import auto_discover_tools
        auto_discover_tools(registry, config, llm_client)

    try:
        context_messages = context_manager.get_recent_messages(
            user_id,
            limit=config["context_limit"],
            format_config=config["context_format"]
        )

        context_manager.set_context("current_user_id", user_id)
        context_manager.set_context("current_query", user_input)
        context_manager.set_context("execution_timestamp", __import__('time').time())

        tool_prompt = _build_tool_selection_prompt(user_input, registry.get_tools(), context_messages, config)

        decision_result = format_response(
            prompt=tool_prompt,
            formatter="json",
            model_name=model,
            temperature=temperature,
            return_meta=True,
            system_prompt=system_prompt,
            messages=context_messages
        )

        if not decision_result or decision_result.get("error"):
            error = decision_result.get("error", "No response")
            tool_decision = {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": f"LLM error: {error}",
                "direct_response": config.get("llm_error_message", "I'm having trouble processing your request.")
            }
        else:
            tool_decision = _extract_tool_decision(decision_result.get("raw_response", ""), config)

        selected_tool = tool_decision.get("selected_tool", "none")
        tool_parameters = tool_decision.get("parameters", {})
        reasoning = tool_decision.get("reasoning", "")
        final_response = ""
        tool_result = None

        if selected_tool != "none":
            tool = registry.get_tool(selected_tool)
            if tool:
                try:
                    enhanced_params = {
                        **tool_parameters,
                        "registry": registry,
                        "config": config,
                        "user_id": user_id,
                        "original_query": user_input,
                        "context_manager": context_manager
                    }

                    if config.get("enable_parameter_enhancement", False):
                        enhanced_params = _enhance_tool_parameters(enhanced_params, tool, config, model, temperature)

                    tool_result = tool.execute(enhanced_params)

                    final_prompt = config.get("final_response_prompt", """
User query: {user_input}
Tool used: {selected_tool}
Result: {tool_result}

Respond naturally to user.
""").format(
                        user_input=user_input,
                        selected_tool=selected_tool,
                        tool_result=tool_result
                    )

                    result = format_response(
                        prompt=final_prompt,
                        formatter="answer",
                        model_name=model,
                        temperature=temperature,
                        return_meta=True,
                        system_prompt=system_prompt
                    )

                    final_response = result.get("parsed_response", str(tool_result))

                except Exception as e:
                    logger.error(f"Tool execution failed: {e}")
                    final_response = _ai_error_recovery(str(e), selected_tool, user_input, registry, config, model, temperature)
            else:
                final_response = config.get("tool_not_found_message", f"Tool '{selected_tool}' not found.")
        else:
            final_response = tool_decision.get("direct_response", "I can help you with that.")
            logger.info("Providing direct response")

        context_manager.add_interaction(user_id, user_input, final_response)

        return {
            "input": user_input,
            "selected_tool": selected_tool,
            "tool_parameters": tool_parameters,
            "tool_result": tool_result,
            "reasoning": reasoning,
            "final_response": final_response,
            "used_model": model,
            "user_id": user_id
        }

    except Exception as e:
        logger.error(f"Agent execution failed: {e}")
        fallback = config.get("general_error_message", "An error occurred: {error}").format(error=str(e))
        try:
            context_manager.add_interaction(user_id, user_input, fallback)
        except Exception:
            logger.error("Failed to store interaction context")

        return {
            "input": user_input,
            "selected_tool": "none",
            "tool_parameters": {},
            "tool_result": None,
            "reasoning": "Unhandled error",
            "final_response": fallback,
            "used_model": model,
            "user_id": user_id,
            "error": str(e)
        }
